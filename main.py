#!/usr/bin/env python3
"""
Main entry point for ML Prediction Service
Simple server for ML predictions via REST API
"""

import sys
import os
import logging
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from fastapi import FastAPI, HTTPException
    from pydantic import BaseModel
    import uvicorn
    from ml_predictor import MLPredictor
    from config import Config
    print("✓ All imports successful!")
except ImportError as e:
    print(f"✗ Import error: {e}")
    print("Please install requirements: pip install -r requirements.txt")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL.upper()))
logger = logging.getLogger(__name__)

# Initialize ML predictor
print("Loading ML predictor...")
ml_predictor = MLPredictor()
print(f"✓ ML model loaded: {ml_predictor.model is not None}")

# FastAPI app
app = FastAPI(
    title=Config.SERVICE_NAME,
    version=Config.SERVICE_VERSION,
    description="Simple ML prediction service for production time estimation"
)

# Request/Response models
class PredictionRequest(BaseModel):
    features: dict  # Expected: {"Width": float, "Thickness": float, "Height": float}

class PredictionResponse(BaseModel):
    prediction: float
    timestamp: str
    status: str = "success"

class ServiceInfo(BaseModel):
    service: str
    version: str
    status: str
    model_loaded: bool
    timestamp: str
    endpoints: dict

# API Endpoints
@app.get(Config.ENDPOINTS["root"], response_model=ServiceInfo)
async def root():
    """Get service information and status"""
    return ServiceInfo(
        service=Config.SERVICE_NAME,
        version=Config.SERVICE_VERSION,
        status="running",
        model_loaded=ml_predictor.model is not None,
        timestamp=datetime.now().isoformat(),
        endpoints={
            "health": Config.ENDPOINTS["health"],
            "predict": Config.ENDPOINTS["predict"],
            "model_info": Config.ENDPOINTS["model_info"]
        }
    )

@app.get(Config.ENDPOINTS["health"])
async def health():
    """Health check endpoint"""
    return {
        "status": "healthy" if ml_predictor.model is not None else "degraded",
        "model_loaded": ml_predictor.model is not None,
        "timestamp": datetime.now().isoformat()
    }

@app.get(Config.ENDPOINTS["model_info"])
async def model_info():
    """Get model information"""
    return {
        "model_info": ml_predictor.get_model_info(),
        "expected_features": ["Width", "Thickness", "Height"],
        "model_parameters": Config.HARDCODED_VALUES,
        "timestamp": datetime.now().isoformat()
    }

@app.post(Config.ENDPOINTS["predict"], response_model=PredictionResponse)
async def predict(request: PredictionRequest):
    """
    Make prediction based on input features

    Expected input format:
    {
        "features": {
            "Width": 50.0,
            "Thickness": 100.0,
            "Height": 25.0
        }
    }
    """
    try:
        logger.info(f"Received prediction request: {request.features}")

        # Validate required features
        required_features = ["Width", "Thickness", "Height"]
        missing_features = [f for f in required_features if f not in request.features]
        if missing_features:
            raise HTTPException(
                status_code=400,
                detail=f"Missing required features: {missing_features}"
            )

        # Make prediction
        prediction = ml_predictor.predict(request.features)

        if prediction is None:
            raise HTTPException(status_code=500, detail="Prediction failed")

        logger.info(f"Prediction result: {prediction}")

        return PredictionResponse(
            prediction=prediction,
            timestamp=datetime.now().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in prediction: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def main():
    """Main function to start the service"""
    print(f" Starting {Config.SERVICE_NAME} v{Config.SERVICE_VERSION}")
    print(f" Service will be available at: http://{Config.HOST}:{Config.PORT}")
    print(f" API Documentation: http://{Config.HOST}:{Config.PORT}/docs")
    print(f" Health Check: http://{Config.HOST}:{Config.PORT}{Config.ENDPOINTS['health']}")

    try:
        uvicorn.run(
            app,
            host=Config.HOST,
            port=Config.PORT,
            log_level=Config.LOG_LEVEL
        )
    except Exception as e:
        print(f"✗ Error starting service: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()